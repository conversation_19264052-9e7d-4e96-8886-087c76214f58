import { useEffect, useRef, useCallback, useState } from 'react'
import type { ResolutionKey } from '@/components/chart/types'
import type { IKLineInfo } from '@/types/kline'

// 🔥 增强的错误类型定义
export enum SSEErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  BROWSER_COMPATIBILITY = 'BROWSER_COMPATIBILITY',
  SERVER_ERROR = 'SERVER_ERROR',
  DATA_VALIDATION = 'DATA_VALIDATION',
  CONNECTION_TIMEOUT = 'CONNECTION_TIMEOUT',
  RATE_LIMIT = 'RATE_LIMIT',
  UNKNOWN = 'UNKNOWN'
}

export interface SSEError {
  type: SSEErrorType
  message: string
  originalError?: Event | Error
  timestamp: number
  retryable: boolean
}

// 🔥 连接质量监控
export interface ConnectionQuality {
  latency: number
  messageCount: number
  errorCount: number
  reconnectCount: number
  lastMessageTime: number
}

interface KlineSSEConfig {
  symbol: string
  bar: ResolutionKey
  enabled?: boolean
  onKlineUpdate?: (klineData: IKLineInfo) => void
  onConnected?: (connectionId: string) => void
  onError?: (error: SSEError) => void // 🔥 增强错误类型
  onConnectionStatusChange?: (
    status: 'connecting' | 'connected' | 'disconnected' | 'error'
  ) => void
  onQualityChange?: (quality: ConnectionQuality) => void // 🔥 连接质量回调
  connectionTimeout?: number // 🔥 连接超时配置
  throttleMs?: number // 🔥 数据节流间隔
}

interface KlineSSEReturn {
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  connect: () => void
  disconnect: () => void
  reconnect: () => void
  lastKlineData: IKLineInfo | null
  connectionQuality: ConnectionQuality // 🔥 连接质量信息
  lastError: SSEError | null // 🔥 最后一次错误
  isOnline: boolean // 🔥 网络状态
  browserSupported: boolean // 🔥 浏览器兼容性
}

export const useKlineSSE = ({
  symbol,
  bar,
  enabled = true,
  onKlineUpdate,
  onConnected,
  onError,
  onConnectionStatusChange,
  onQualityChange,
  connectionTimeout = 10000, // 🔥 默认10秒连接超时
  throttleMs = 100 // 🔥 默认100ms数据节流
}: KlineSSEConfig): KlineSSEReturn => {
  const eventSourceRef = useRef<EventSource | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<
    'connecting' | 'connected' | 'disconnected' | 'error'
  >('disconnected')
  const [lastKlineData, setLastKlineData] = useState<IKLineInfo | null>(null)
  const [lastError, setLastError] = useState<SSEError | null>(null)
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const [browserSupported] = useState(() => typeof EventSource !== 'undefined')

  // 🔥 关键修复：使用useRef存储回调函数，避免依赖数组问题
  const onKlineUpdateRef = useRef(onKlineUpdate)
  const onConnectedRef = useRef(onConnected)
  const onErrorRef = useRef(onError)
  const onConnectionStatusChangeRef = useRef(onConnectionStatusChange)
  const onQualityChangeRef = useRef(onQualityChange)

  // 更新ref中的回调函数
  onKlineUpdateRef.current = onKlineUpdate
  onConnectedRef.current = onConnected
  onErrorRef.current = onError
  onConnectionStatusChangeRef.current = onConnectionStatusChange
  onQualityChangeRef.current = onQualityChange
  const [connectionQuality, setConnectionQuality] = useState<ConnectionQuality>(
    {
      latency: 0,
      messageCount: 0,
      errorCount: 0,
      reconnectCount: 0,
      lastMessageTime: 0
    }
  )

  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const connectionTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastThrottleTimeRef = useRef<number>(0)
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5
  const connectionStartTime = useRef<number>(0)

  // 🔥 错误分类和处理函数
  const classifyError = useCallback(
    (error: Event | Error, context?: string): SSEError => {
      let errorType = SSEErrorType.UNKNOWN
      let message = 'Unknown error occurred'
      let retryable = true

      if (error instanceof Error) {
        if (
          error.message.includes('Failed to fetch') ||
          error.message.includes('Network')
        ) {
          errorType = SSEErrorType.NETWORK_ERROR
          message =
            'Network connection failed, please check your internet connection'
        } else if (error.message.includes('EventSource')) {
          errorType = SSEErrorType.BROWSER_COMPATIBILITY
          message = 'Browser does not support EventSource functionality'
          retryable = false
        } else if (error.message.includes('timeout')) {
          errorType = SSEErrorType.CONNECTION_TIMEOUT
          message = 'Connection timeout, please try again later'
        } else if (error.message.includes('rate limit')) {
          errorType = SSEErrorType.RATE_LIMIT
          message = 'Too many requests, please try again later'
        } else if (context === 'data_validation') {
          errorType = SSEErrorType.DATA_VALIDATION
          message = `Data validation failed: ${error.message}`
          retryable = false
        }
      } else {
        // EventSource错误事件
        errorType = SSEErrorType.SERVER_ERROR
        message = 'Server connection error, attempting to reconnect...'
      }

      return {
        type: errorType,
        message,
        originalError: error,
        timestamp: Date.now(),
        retryable
      }
    },
    []
  )

  const updateConnectionStatus = useCallback(
    (status: 'connecting' | 'connected' | 'disconnected' | 'error') => {
      setConnectionStatus(status)
      onConnectionStatusChange?.(status)
    },
    [onConnectionStatusChange]
  )

  // 🔥 网络状态监控
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true)
      console.log('🌐 Network connected, attempting to reconnect SSE')
      if (enabled && symbol && bar) {
        reconnect()
      }
    }

    const handleOffline = () => {
      setIsOnline(false)
      console.log('🌐 Network disconnected, closing SSE connection')
      disconnect()
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [enabled, symbol, bar]) // eslint-disable-line react-hooks/exhaustive-deps

  const disconnect = useCallback(() => {
    console.log('🔌 Disconnecting SSE connection...')

    // 🔥 清理EventSource连接
    if (eventSourceRef.current) {
      // 检查连接状态，避免重复关闭
      if (eventSourceRef.current.readyState !== EventSource.CLOSED) {
        eventSourceRef.current.close()
      }
      eventSourceRef.current = null
    }

    // 🔥 清理所有定时器
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    if (connectionTimeoutRef.current) {
      clearTimeout(connectionTimeoutRef.current)
      connectionTimeoutRef.current = null
    }

    updateConnectionStatus('disconnected')
    reconnectAttempts.current = 0
    connectionStartTime.current = 0
  }, [updateConnectionStatus])

  const connect = useCallback(() => {
    // 🔥 浏览器兼容性检查
    if (!browserSupported) {
      const error = classifyError(
        new Error('EventSource not supported'),
        'browser_check'
      )
      setLastError(error)
      onErrorRef.current?.(error)
      updateConnectionStatus('error')
      return
    }

    // 🔥 网络状态检查
    if (!isOnline) {
      const error = classifyError(
        new Error('Network unavailable'),
        'network_check'
      )
      setLastError(error)
      onErrorRef.current?.(error)
      console.log('🌐 Network unavailable, skipping SSE connection')
      return
    }

    // 🔥 增强边界条件检查
    if (!enabled || !symbol || !bar) {
      console.log(
        '🚫 SSE connection conditions not met - enabled:',
        enabled,
        'symbol:',
        symbol,
        'bar:',
        bar
      )
      return
    }

    // 🔥 防止并发连接：如果正在连接中，先等待断开
    if (connectionStatus === 'connecting') {
      console.log(
        '⏳ SSE connection in progress, waiting for current connection to complete...'
      )
      return
    }

    // 关闭已有连接
    disconnect()

    // 🔥 增强URL验证
    const trimmedSymbol = symbol.trim()
    if (!trimmedSymbol) {
      const error = classifyError(
        new Error('Symbol cannot be empty'),
        'validation'
      )
      setLastError(error)
      onErrorRef.current?.(error)
      updateConnectionStatus('error')
      return
    }

    const url = `https://teststream.pebble-finance.com/stream/kline?symbol=${encodeURIComponent(
      trimmedSymbol
    )}&bar=${encodeURIComponent(bar)}`

    try {
      updateConnectionStatus('connecting')
      connectionStartTime.current = Date.now()
      console.log('🔗 Starting SSE connection - URL:', url)

      const eventSource = new EventSource(url)
      eventSourceRef.current = eventSource

      // 🔥 连接超时处理
      connectionTimeoutRef.current = setTimeout(() => {
        if (eventSource.readyState === EventSource.CONNECTING) {
          console.error('⏰ SSE connection timeout')
          const error = classifyError(
            new Error('Connection timeout'),
            'timeout'
          )
          setLastError(error)
          onErrorRef.current?.(error)
          eventSource.close()
          updateConnectionStatus('error')

          // 🔥 更新连接质量统计
          setConnectionQuality((prev) => ({
            ...prev,
            errorCount: prev.errorCount + 1
          }))
        }
      }, connectionTimeout)

      // 连接成功事件
      eventSource.addEventListener('connected', (event) => {
        // 🔥 清理连接超时定时器
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current)
          connectionTimeoutRef.current = null
        }

        updateConnectionStatus('connected')
        reconnectAttempts.current = 0

        // 🔥 计算连接延迟
        const latency = Date.now() - connectionStartTime.current
        setConnectionQuality((prev) => ({
          ...prev,
          latency,
          reconnectCount:
            prev.reconnectCount + (reconnectAttempts.current > 0 ? 1 : 0)
        }))

        onConnectedRef.current?.(event.data)
        console.log(
          '🔗 SSE connected successfully, ID:',
          event.data,
          'Latency:',
          latency + 'ms'
        )
      })

      // K线数据事件
      eventSource.addEventListener('kline', (event) => {
        try {
          // 🔥 数据节流控制
          const now = Date.now()
          if (now - lastThrottleTimeRef.current < throttleMs) {
            console.log('🚦 Data throttled, skipping update')
            return
          }
          lastThrottleTimeRef.current = now

          const klineData: IKLineInfo = JSON.parse(event.data)

          // 🔥 增强数据校验
          if (!klineData || typeof klineData !== 'object') {
            throw new Error('Invalid kline data format received')
          }

          // 🔥 校验必要字段
          const requiredFields = ['t', 'o', 'h', 'l', 'c'] as const
          for (const field of requiredFields) {
            if (
              typeof klineData[field] !== 'number' ||
              isNaN(klineData[field])
            ) {
              throw new Error(
                `Invalid kline field ${field}: ${klineData[field]}`
              )
            }
          }

          // 🔥 数据合理性检查
          if (klineData.h < klineData.l) {
            throw new Error(
              `Invalid kline data: High(${klineData.h}) < Low(${klineData.l})`
            )
          }

          if (
            klineData.o < 0 ||
            klineData.c < 0 ||
            klineData.h < 0 ||
            klineData.l < 0
          ) {
            throw new Error(
              `Kline data contains negative values: O:${klineData.o} H:${klineData.h} L:${klineData.l} C:${klineData.c}`
            )
          }

          // 🔥 时间戳有效性检查
          if (klineData.t > now + 60000) {
            // 允许1分钟的时间偏差
            throw new Error(
              `Kline timestamp too far in future: ${klineData.t} > ${now}`
            )
          }

          // 🔥 检查数据是否过时（超过5分钟的数据视为过时）
          if (now - klineData.t > 5 * 60 * 1000) {
            console.warn(
              '⚠️ Received stale kline data, timestamp:',
              klineData.t
            )
          }

          // 🔥 更新连接质量统计
          setConnectionQuality((prev) => ({
            ...prev,
            messageCount: prev.messageCount + 1,
            lastMessageTime: now
          }))

          console.log(`klineData: `, klineData)
          debugger;
          setLastKlineData(klineData)
          onKlineUpdateRef.current?.(klineData)
          console.log('📊 Valid kline data received:', klineData)
        } catch (error) {
          console.error('❌ Kline data processing failed:', error)
          const classifiedError = classifyError(
            error as Error,
            'data_validation'
          )
          setLastError(classifiedError)
          onErrorRef.current?.(classifiedError)

          // 🔥 更新错误统计
          setConnectionQuality((prev) => ({
            ...prev,
            errorCount: prev.errorCount + 1
          }))
        }
      })

      // 连接错误处理
      eventSource.onerror = (error) => {
        console.error('🔗 SSE connection error:', error)

        // 🔥 清理连接超时定时器
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current)
          connectionTimeoutRef.current = null
        }

        updateConnectionStatus('error')

        // 🔥 分类错误并处理
        const classifiedError = classifyError(error, 'connection')
        setLastError(classifiedError)
        onErrorRef.current?.(classifiedError)

        // 🔥 更新连接质量统计
        setConnectionQuality((prev) => ({
          ...prev,
          errorCount: prev.errorCount + 1
        }))

        // 🔥 增强自动重连逻辑
        if (
          classifiedError.retryable &&
          reconnectAttempts.current < maxReconnectAttempts &&
          isOnline
        ) {
          reconnectAttempts.current++
          const delay = Math.min(
            1000 * Math.pow(2, reconnectAttempts.current),
            30000
          ) // 指数退避，最大30秒

          console.log(
            `🔄 Attempting reconnect ${reconnectAttempts.current}/${maxReconnectAttempts} in ${delay}ms...`
          )

          reconnectTimeoutRef.current = setTimeout(() => {
            if (enabled && isOnline) {
              // 🔥 重连前再次检查网络状态
              connect()
            }
          }, delay)
        } else {
          const reason = !classifiedError.retryable
            ? 'error not retryable'
            : !isOnline
              ? 'network offline'
              : 'max reconnect attempts reached'
          console.error(`❌ Stopped reconnecting: ${reason}`)
          updateConnectionStatus('error')
        }
      }

      // 连接打开事件
      eventSource.onopen = () => {
        console.log('🔗 SSE connection opened:', url)
      }
    } catch (error) {
      console.error('❌ Failed to create SSE connection:', error)
      const classifiedError = classifyError(
        error as Error,
        'connection_creation'
      )
      setLastError(classifiedError)
      updateConnectionStatus('error')
      onErrorRef.current?.(classifiedError)

      // 🔥 更新错误统计
      setConnectionQuality((prev) => ({
        ...prev,
        errorCount: prev.errorCount + 1
      }))
    }
  }, [
    // 🔥 关键修复：移除回调函数依赖，只保留稳定的值
    symbol,
    bar,
    enabled,
    connectionStatus,
    browserSupported,
    isOnline,
    connectionTimeout,
    throttleMs,
    updateConnectionStatus,
    classifyError,
    disconnect
  ])

  const reconnect = useCallback(() => {
    console.log('🔄 Manual reconnect triggered')
    reconnectAttempts.current = 0

    // 🔥 清理任何现有的重连定时器
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    connect()
  }, [connect])

  // 当参数变化时重新连接 - 优化后的单一清理逻辑
  useEffect(() => {
    console.log(
      '🔄 SSE parameters changed - symbol:',
      symbol,
      'bar:',
      bar,
      'enabled:',
      enabled
    )

    if (enabled && symbol && bar) {
      connect()
    } else {
      disconnect()
    }

    // 🔥 关键：确保参数变化时旧连接被清理
    return () => {
      console.log('🧹 Cleaning up SSE connection - symbol:', symbol)
      disconnect()
    }
  }, [symbol, bar, enabled, connect, disconnect])

  // 🔥 触发连接质量变化回调
  useEffect(() => {
    onQualityChange?.(connectionQuality)
  }, [connectionQuality, onQualityChange])

  return {
    connectionStatus,
    connect,
    disconnect,
    reconnect,
    lastKlineData,
    connectionQuality,
    lastError,
    isOnline,
    browserSupported
  }
}

import { useGetMarginTradeKline } from '@/queries/kline'
import { useMemo, useEffect, useRef, useState } from 'react'
import { ResolutionKey } from '@/components/chart/types'

export interface ILeveragePriceResult {
  currentPrice: number
  priceChange24h: number
  priceChangePercent24h: number
  isLoading: boolean
  error: string | null
}

export const useLeveragePrice = (
  coinType: string,
  options?: {
    refetchInterval?: number | false
    enabled?: boolean
  }
): ILeveragePriceResult => {
  // 🔍 调试：追踪hook调用
  console.log('🔄 useLeveragePrice called with:', { coinType, enabled: options?.enabled })

  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // 使用 useState 管理时间戳，初始计算一次
  const [timeRange, setTimeRange] = useState(() => {
    const now = Date.now()
    const twentyFourHoursAgo = now - 24 * 60 * 60 * 1000
    return {
      fromTime: twentyFourHoursAgo,
      toTime: now
    }
  })

  // 获取K线数据
  const {
    data: klineData,
    isLoading,
    error
  } = useGetMarginTradeKline(
    {
      address: coinType,
      fromTime: timeRange.fromTime,
      toTime: timeRange.toTime,
      size: 50,
      bar: ResolutionKey.Hour1 // 使用1小时K线数据
    },
    {
      enabled: options?.enabled !== false && !!coinType,
      refetchInterval: false, // 禁用 TanStack Query 的自动刷新
      staleTime: 10000 // 10秒内认为数据是新鲜的
    }
  )

  // 🔥 优化：coinType变化时重置时间范围
  useEffect(() => {
    if (coinType) {
      const now = Date.now()
      const twentyFourHoursAgo = now - 24 * 60 * 60 * 1000
      setTimeRange({
        fromTime: twentyFourHoursAgo,
        toTime: now
      })
    }
  }, [coinType])

  // 设置定时器进行手动刷新
  useEffect(() => {
    // 清除之前的定时器
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }

    // 如果启用了定时刷新且有 coinType
    if (options?.refetchInterval && options.refetchInterval > 0 && coinType) {
      intervalRef.current = setInterval(() => {
        // 🔥 优化：直接更新时间戳，避免频繁的函数式更新检查
        const now = Date.now()
        const twentyFourHoursAgo = now - 24 * 60 * 60 * 1000
        setTimeRange({
          fromTime: twentyFourHoursAgo,
          toTime: now
        })
      }, options.refetchInterval)
    }

    // 清理函数
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [options?.refetchInterval, coinType])

  // 计算价格数据
  const priceData = useMemo(() => {
    // 🔍 调试：追踪价格数据计算
    console.log('🔄 Calculating price data for:', coinType, 'data length:', klineData?.data?.length)

    // 检查数据结构 - 与 Chart() 组件保持一致
    if (
      !klineData?.data ||
      !Array.isArray(klineData.data) ||
      klineData.data.length === 0
    ) {
      return {
        currentPrice: 0,
        priceChange24h: 0,
        priceChangePercent24h: 0
      }
    }

    // 获取最新价格（最后一个K线数据的收盘价）
    const latestKline = klineData.data[klineData.data.length - 1]
    const currentPrice = latestKline.c

    // 获取24小时前的价格（第一个K线数据的开盘价）
    const oldestKline = klineData.data[0]
    const price24hAgo = oldestKline.o

    // 计算价格变化
    const priceChange24h = currentPrice - price24hAgo
    const priceChangePercent24h =
      price24hAgo > 0 ? (priceChange24h / price24hAgo) * 100 : 0

    const result = {
      currentPrice,
      priceChange24h,
      priceChangePercent24h
    }

    console.log('💰 Price data calculated:', result)
    return result
  }, [klineData, coinType]) // 🔥 添加coinType到依赖数组，确保coinType变化时重新计算

  return {
    currentPrice: priceData.currentPrice,
    priceChange24h: priceData.priceChange24h,
    priceChangePercent24h: priceData.priceChangePercent24h,
    isLoading,
    error: error?.message || null
  }
}
